import { RouterProvider } from 'react-router-dom';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import router from './router';
import './App.css';
import { useEffect, useState } from 'react';
import { HelmetProvider } from 'react-helmet-async';

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // 监听暗黑模式变化
    const root = document.documentElement;
    const observer = new MutationObserver(() => {
      setIsDarkMode(root.classList.contains('dark'));
    });

    observer.observe(root, { attributes: true });

    // 初始检查
    setIsDarkMode(root.classList.contains('dark'));

    return () => observer.disconnect();
  }, []);

  return (
    <HelmetProvider>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1a8fe3',
            colorBgContainer: isDarkMode ? '#1f2d38' : '#f8fbfe',
            colorText: isDarkMode ? '#ffffff' : '#333333',
          },
        }}
      >
        <RouterProvider router={router} />
      </ConfigProvider>
    </HelmetProvider>
  );
}

export default App;
