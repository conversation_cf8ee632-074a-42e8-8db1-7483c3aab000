import { create } from 'zustand';
import zhHomeTools from '../pages/home/<USER>';
import enHomeTools from '../pages/home/<USER>';
import zhProductivityTools from '../pages/productivity/tools';
import enProductivityTools from '../pages/productivity/tools.en';
import zhGenerationTools from '../pages/generation/tools';
import enGenerationTools from '../pages/generation/tools.en';
import zhDesignTools from '../pages/design/tools';
import enDesignTools from '../pages/design/tools.en';
import i18next from 'i18next';

export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  price: string;
  url?: string;
}

export interface HomeTool {
  name: string;
  description: string;
  image_url: string;
  link: string;
}

export type PageType = 'home' | 'productivity' | 'generation' | 'design';

interface ToolsState {
  tools: Tool[];
  homeTools: HomeTool[];
  categories: string[];
  selectedCategory: string;
  searchText: string;
  homeSearchText: string;
  currentPage: PageType;
  setSelectedCategory: (category: string) => void;
  setSearchText: (text: string) => void;
  setHomeSearchText: (text: string) => void;
  setCurrentPage: (page: PageType) => void;
  addTool: (tool: Tool) => void;
  removeTool: (id: string) => void;
  updateTool: (tool: Tool) => void;
  getFilteredPageTools: () => HomeTool[];
  getPageTools: (page?: PageType) => HomeTool[];
}

export const useToolsStore = create<ToolsState>((set, get) => ({
  tools: [
    {
      id: '1',
      name: 'Trae',
      description: '提升开发者协作与效率的人工智能集成开发环境',
      category: 'AI开发者工具',
      tags: ['IDE', '开发效率', '协作'],
      price: 'Free',
    },
    {
      id: '2',
      name: 'iFable',
      description: '由AI驱动的动漫角色扮演游戏，用于创建故事和情感联系',
      category: 'AI角色扮演',
      tags: ['游戏', '动漫', '故事创作'],
      price: 'Free',
    },
    {
      id: '3',
      name: 'Wonderchat',
      description: 'AI聊天机器人构建工具，允许从网站链接或PDF创建自定义ChatGPT聊天机器人',
      category: 'AI聊天机器人',
      tags: ['聊天机器人', 'ChatGPT', '自定义'],
      price: 'Free',
    },
  ],
  homeTools: zhHomeTools,
  categories: [
    'AI开发者工具',
    'AI角色扮演',
    'AI聊天机器人',
    'AI写作助手',
    'AI图像生成器',
    'AI音乐生成器',
  ],
  selectedCategory: '',
  searchText: '',
  homeSearchText: '',
  currentPage: 'home',

  setSelectedCategory: category => set({ selectedCategory: category }),
  setSearchText: text => set({ searchText: text }),
  setHomeSearchText: text => set({ homeSearchText: text }),
  setCurrentPage: page => set({ currentPage: page }),

  getPageTools: page => {
    const currentLanguage = i18next.language;
    const pageToUse = page || get().currentPage;

    const toolsMap = {
      home: currentLanguage === 'zh' ? zhHomeTools : enHomeTools,
      productivity: currentLanguage === 'zh' ? zhProductivityTools : enProductivityTools,
      generation: currentLanguage === 'zh' ? zhGenerationTools : enGenerationTools,
      design: currentLanguage === 'zh' ? zhDesignTools : enDesignTools,
    };

    return toolsMap[pageToUse];
  },

  getFilteredPageTools: () => {
    const state = get();
    const searchText = state.homeSearchText.toLowerCase().trim();
    const tools = state.getPageTools();

    if (!searchText) {
      return tools;
    }

    return tools.filter(
      tool =>
        tool.name.toLowerCase().includes(searchText) ||
        tool.description.toLowerCase().includes(searchText),
    );
  },

  addTool: tool => set(state => ({ tools: [...state.tools, tool] })),
  removeTool: id =>
    set(state => ({
      tools: state.tools.filter(tool => tool.id !== id),
    })),
  updateTool: tool =>
    set(state => {
      const index = state.tools.findIndex(t => t.id === tool.id);
      if (index !== -1) {
        const updatedTools = [...state.tools];
        updatedTools[index] = tool;
        return { tools: updatedTools };
      }
      return state;
    }),
}));
