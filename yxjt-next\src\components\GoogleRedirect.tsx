import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

const GoogleRedirect = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    // 获取目标URL
    const targetUrl = searchParams.get('url') || '/';
    
    // 清除URL参数并导航到目标页面
    navigate(targetUrl, { replace: true });
  }, [navigate, searchParams]);

  return (
    <div className="flex items-center justify-center h-screen">
      <p>Redirecting...</p>
    </div>
  );
};

export default GoogleRedirect;