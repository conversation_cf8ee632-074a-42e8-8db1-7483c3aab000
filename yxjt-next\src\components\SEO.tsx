import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  type?: string;
}

const SEO = ({ 
  title, 
  description, 
  keywords = 'AI tools, productivity, design, programming, AI assistant', 
  image = '/logo.svg',
  type = 'website'
}: SEOProps) => {
  const { t } = useTranslation();
  const location = useLocation();
  const currentPage = location.pathname.split('/').pop() || 'home';
  
  const pageTitle = title || `${t(currentPage)} | Tooltide`;
  const pageDescription = description || t(`${currentPage}Description`, 'Discover the best AI tools for productivity, programming, and design');
  const canonicalUrl = `${window.location.origin}${location.pathname}`;

  return (
    <Helmet>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:image" content={`${window.location.origin}${image}`} />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={canonicalUrl} />
      <meta property="twitter:title" content={pageTitle} />
      <meta property="twitter:description" content={pageDescription} />
      <meta property="twitter:image" content={`${window.location.origin}${image}`} />
      
      <link rel="canonical" href={canonicalUrl} />
    </Helmet>
  );
};

export default SEO;