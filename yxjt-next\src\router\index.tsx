import { createBrowserRouter } from 'react-router-dom';
import { lazy } from 'react';
import Layout from '../layout';
// import RedirectHandler from '../components/RedirectHandler';
import GoogleRedirect from '../components/GoogleRedirect';

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        Component: lazy(() => import('../pages/home')),
      },
    ],
  },
  {
    path: '/google-redirect',
    element: <GoogleRedirect />,
  },
]);

export default router;
