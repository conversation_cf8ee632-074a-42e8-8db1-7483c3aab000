import { Layout as AntLayout, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import FloatingLottie from '../components/FloatingLottie';
import { useToolsStore } from '../store/useToolsStore';
import { useTranslation } from 'react-i18next';

const { Content } = AntLayout;

const Layout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { homeSearchText, setHomeSearchText } = useToolsStore();
  const { t } = useTranslation();

  // Only show search functionality on home page
  const isHomePage = location.pathname === '/';

  const handleSearch = () => {
    if (!isHomePage) {
      navigate('/');
    }
  };
  // 获取当前页面的路径
  const currentPage = location.pathname.split('/').pop() || 'home';

  return (
    <AntLayout className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <AntLayout
        className="mt-[66px] w-full px-4 py-6"
        style={{ backgroundColor: 'var(--bg-primary)', minHeight: 'calc(100vh - 66px)' }}
      >
        <Content className="w-full max-w-[1400px] mx-auto">
          <h1
            className="w-full text-center font-bold text-[56px]"
            style={{ color: 'var(--text-primary)' }}
          >
            {currentPage}
          </h1>
          <div className="w-[480px] mx-auto mt-6 mb-12">
            <Input
              placeholder={t('searchAiTools')}
              prefix={<SearchOutlined style={{ color: '#1a8fe3' }} />}
              value={homeSearchText}
              onChange={e => setHomeSearchText(e.target.value)}
              onPressEnter={handleSearch}
              suffix={
                <button
                  className="w-[100px] h-[48px] rounded-full bg-[#1a8fe3] text-white cursor-pointer hover:bg-[#1677ff] transition-colors"
                  style={{
                    fontSize: '16px',
                  }}
                  onClick={handleSearch}
                >
                  {t('search')}
                </button>
              }
              style={{
                height: '48px',
                borderRadius: '24px',
                border: '1px solid var(--border-color)',
                backgroundColor: 'var(--component-bg)',
                fontSize: '20px',
                color: 'var(--text-primary)',
              }}
              className=""
            />
          </div>
          <Outlet />
        </Content>
      </AntLayout>
      <FloatingLottie />
    </AntLayout>
  );
};

export default Layout;
