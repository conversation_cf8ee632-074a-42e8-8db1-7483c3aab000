# 处理Google重定向
<IfModule mod_rewrite.c>
  RewriteEngine On
  
  # 检查是否来自Google
  RewriteCond %{HTTP_REFERER} google\.[a-z.]+$ [NC]
  
  # 如果URL包含特定参数，重定向到干净的URL
  RewriteCond %{QUERY_STRING} ^(.*)url=([^&]+)(.*)$ [NC]
  RewriteRule ^(.*)$ %2? [R=301,L]
  
  # 确保所有路由都指向index.html（SPA应用）
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>