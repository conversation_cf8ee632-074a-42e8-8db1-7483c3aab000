import { Helmet } from 'react-helmet-async';

interface ToolListProps {
  tools: Array<{
    name: string;
    description: string;
    image_url?: string;
    link?: string;
  }>;
  pageType: string;
}

const StructuredData = ({ tools, pageType }: ToolListProps) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    'itemListElement': tools.map((tool, index) => ({
      '@type': 'ListItem',
      'position': index + 1,
      'item': {
        '@type': 'SoftwareApplication',
        'name': tool.name,
        'description': tool.description,
        'image': tool.image_url,
        'url': tool.link,
        'applicationCategory': pageType,
        'offers': {
          '@type': 'Offer',
          'price': '0',
          'priceCurrency': 'USD'
        }
      }
    }))
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default StructuredData;