import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Translation resources
const resources = {
  en: {
    translation: {
      // Home page
      aiTools: 'AI Tools',
      searchAiTools: 'Search AI tools...',
      search: 'Search',
      noToolsFound: 'No AI tools found containing "{{searchText}}"',
      noTools: 'No AI tools available',

      // Categories
      home: 'AI Tools',
      productivity: 'Productivity Tools',
      generation: 'Programming Tools',
      design: 'File Tools',

      // SEO Descriptions
      homeDescription:
        'Discover the best AI tools to boost your productivity and creativity. Find ChatGP<PERSON>, <PERSON>, and other top AI assistants.',
      productivityDescription:
        'Explore top productivity tools like Notion, Trello and more to streamline your workflow and boost efficiency.',
      generationDescription:
        'Find the best programming and code generation tools powered by AI to accelerate your development process.',
      designDescription:
        'Discover powerful file and design tools to enhance your creative workflow and file management.',

      // Header
      login: 'Login',
      lightMode: 'Switch to Light Mode',
      darkMode: 'Switch to Dark Mode',
      copySuccess: 'URL copied to clipboard!',
      share: 'Share',
    },
  },
  zh: {
    translation: {
      // Home page
      aiTools: 'AI工具',
      searchAiTools: '搜索AI工具...',
      search: '搜索',
      noToolsFound: '未找到包含 "{{searchText}}" 的AI工具',
      noTools: '暂无AI工具',

      // Categories
      home: 'AI工具',
      productivity: '效率工具',
      generation: '编程工具',
      design: '文件工具',

      // SEO Descriptions
      homeDescription:
        '发现最佳AI工具，提升您的生产力和创造力。查找ChatGPT、Gemini和其他顶级AI助手。',
      productivityDescription: '探索Notion、Trello等顶级效率工具，简化工作流程，提高效率。',
      generationDescription: '寻找由AI驱动的最佳编程和代码生成工具，加速您的开发过程。',
      designDescription: '发现强大的文件和设计工具，增强您的创意工作流程和文件管理。',

      // Header
      login: '登录',
      lightMode: '切换到亮色模式',
      darkMode: '切换到暗色模式',
      copySuccess: '网址已复制到剪贴板！',
      share: '分享',
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh',
    interpolation: {
      escapeValue: false, // React already safes from XSS
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
