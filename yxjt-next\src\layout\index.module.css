.box {
  width: 100%;
  height: 66px;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  font-size: 18px;
}

.title {
  height: 100%;
  font-size: 26px;
  color: #333;
  display: flex;
  align-items: center;
  font-family: 'Times New Roman', '宋体', serif;
  font-style: italic;
}
.title img {
  margin-right: 4px;
  margin-top: -6px;
}

.dark {
  background: #223544;
  border-color: #828586;
}
.moon {
  background: #fff;
}
.no-hover-button:hover {
  background-color: transparent !important;
}