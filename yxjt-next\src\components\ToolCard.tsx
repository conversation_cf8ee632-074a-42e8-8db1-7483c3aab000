import { Tag } from 'antd';

interface ToolCardProps {
  name: string;
  description: string;
  image_url: string;
  tags?: string[];
  benefits?: string[];
  link?: string;
}

const ToolCard = ({
  name,
  description,
  image_url,
  tags = [],
  benefits = [],
  link,
}: ToolCardProps) => {
  const handleClick = () => {
    if (link) window.open(link, '_blank');
  };

  return (
    <div
      className="rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer"
      style={{ backgroundColor: 'var(--component-bg)' }}
      onClick={handleClick}
    >
      <div className="p-5 flex flex-col h-full">
        <div className="flex gap-3 justify-center items-center">
          <img src={image_url} className="w-12 h-12 rounded-lg object-cover" alt={name} />
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>
              {name}
            </h3>
            <p className="text-sm line-clamp-1" style={{ color: 'var(--text-secondary)' }}>
              {description}
            </p>
          </div>
        </div>

        {benefits.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">包含功能：</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-1">
                  <span className="w-1 h-1 bg-blue-500 rounded-full"></span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {tags.length > 0 && (
          <div className="mt-auto pt-4 flex flex-wrap gap-2">
            {tags.map(tag => (
              <Tag key={tag} color="blue" className="rounded-full text-xs">
                {tag}
              </Tag>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolCard;
