import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const RedirectHandler = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // 处理来自Google的重定向
    const handleGoogleRedirect = () => {
      const referrer = document.referrer;
      const isFromGoogle = referrer.includes('google.com') || 
                           referrer.includes('google.co') || 
                           referrer.includes('gstatic.com');
      
      // 检查URL中是否有重定向参数
      const urlParams = new URLSearchParams(window.location.search);
      const redirectPath = urlParams.get('redirect');
      
      if (isFromGoogle && redirectPath) {
        // 清除URL中的重定向参数并导航到正确的路径
        navigate(redirectPath, { replace: true });
      }
    };

    handleGoogleRedirect();
  }, [location, navigate]);

  return null; // 这是一个功能性组件，不需要渲染任何内容
};

export default RedirectHandler;