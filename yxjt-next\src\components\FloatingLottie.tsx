import { useLottie } from 'lottie-react';
import { useEffect, useState } from 'react';

const FloatingLottie = () => {
  const [animationData, setAnimationData] = useState(null);

  useEffect(() => {
    // 加载动画JSON文件
    fetch('https://assets2.lottiefiles.com/packages/lf20_kkflmtur.json')
      .then(response => response.json())
      .then(data => setAnimationData(data))
      .catch(error => console.error('Error loading animation:', error));
  }, []);

  const options = {
    animationData,
    loop: true,
    autoplay: true,
  };

  const { View } = useLottie(options);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <div
      className="fixed bottom-6 right-6 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform z-50"
      onClick={scrollToTop}
      title="Scroll to top"
    >
      {View}
    </div>
  );
};

export default FloatingLottie;
